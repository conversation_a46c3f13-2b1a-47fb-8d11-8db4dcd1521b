

import sys
import asyncio
from pathlib import Path

# Add the project's parent directory to the Python path.
# This allows modules like 'settings' and 'core' to be found correctly.
# Correct Path: .../agbase_admin/gaia/
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from gaia_ceto_v2.core.mcp_client import MCPClient

async def main():
    """Test MCP client and logging."""
    print("--- Running MCP Logging Test ---")
    try:
        # Assuming the MCP server is running and has the echostring tool
        client = MCPClient(server_url="http://localhost:9000/mcp/")
        
        print("\n1. Calling 'echostring' tool...")
        result = await client.execute_tool("echostring", {"text": "hello logging world"})
        
        print(f"\n2. Result from echostring: {result}")
        
    except Exception as e:
        print(f"\n--- Test Failed ---")
        print(f"Error: {e}")
        print("\nPlease ensure the MCP server is running on http://localhost:9000/mcp/")
        print("You can start it with: python -m gaia_ceto_v2.mcp_interface.mcp_server")
    else:
        print("\n--- Test Potentially Successful ---")
        print("Check the contents of /tmp/gaia_logs/ceto/ for mcpwrap*.log files.")

if __name__ == "__main__":
    asyncio.run(main())
